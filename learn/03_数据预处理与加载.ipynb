# 导入必要的库
import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from PIL import Image
import re
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 配置matplotlib中文字体，解决中文乱码问题
plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False
print("✅ 已配置matplotlib中文字体")

# 设置项目路径
project_root = os.path.abspath('..')
sys.path.append(project_root)

print(f"项目根目录: {project_root}")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")

def create_image_transforms(is_training=True, image_size=224):
    """
    创建图像预处理变换
    
    Args:
        is_training (bool): 是否为训练模式
        image_size (int): 目标图像尺寸
    
    Returns:
        transforms.Compose: 图像变换组合
    """
    if is_training:
        # 训练时的数据增强
        transform = transforms.Compose([
            transforms.Resize(256),  # 先放大到256
            transforms.RandomCrop(image_size),  # 随机裁剪到目标尺寸
            transforms.RandomHorizontalFlip(p=0.5),  # 随机水平翻转
            transforms.ToTensor(),  # 转换为张量
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],  # ImageNet预训练模型的标准化参数
                std=[0.229, 0.224, 0.225]
            )
        ])
    else:
        # 验证/测试时的标准化处理
        transform = transforms.Compose([
            transforms.Resize((image_size, image_size)),  # 直接缩放到目标尺寸
            transforms.ToTensor(),
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            )
        ])
    
    return transform

# 创建训练和验证的图像变换
train_transform = create_image_transforms(is_training=True)
val_transform = create_image_transforms(is_training=False)

print("训练时图像变换:")
print(train_transform)
print("\n验证时图像变换:")
print(val_transform)

def visualize_image_preprocessing(image_path, transform, title="图像预处理效果"):
    """
    可视化图像预处理效果
    
    Args:
        image_path (str): 图像路径
        transform: 图像变换
        title (str): 图表标题
    """
    try:
        # 加载原始图像
        original_img = Image.open(image_path).convert('RGB')
        
        # 应用变换
        transformed_tensor = transform(original_img)
        
        # 反标准化以便可视化
        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
        transformed_img = transformed_tensor * std + mean
        transformed_img = torch.clamp(transformed_img, 0, 1)
        
        # 转换为numpy数组用于显示
        transformed_np = transformed_img.permute(1, 2, 0).numpy()
        
        # 可视化
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        
        # 原始图像
        axes[0].imshow(original_img, cmap='gray')
        axes[0].set_title(f"原始图像 {original_img.size}")
        axes[0].axis('off')
        
        # 预处理后图像
        axes[1].imshow(transformed_np)
        axes[1].set_title(f"预处理后 {transformed_tensor.shape[1:]}")
        axes[1].axis('off')
        
        plt.suptitle(title)
        plt.tight_layout()
        plt.show()
        
        # 打印张量信息
        print(f"原始图像尺寸: {original_img.size}")
        print(f"预处理后张量形状: {transformed_tensor.shape}")
        print(f"像素值范围: [{transformed_tensor.min():.3f}, {transformed_tensor.max():.3f}]")
        
    except Exception as e:
        print(f"处理图像时出错: {e}")

# 尝试加载一张示例图像进行演示
annotation_path = os.path.join(project_root, 'data', 'iu_xray', 'annotation.json')
image_dir = os.path.join(project_root, 'data', 'iu_xray', 'images')

try:
    with open(annotation_path, 'r') as f:
        data = json.load(f)
    
    # 找到第一个存在的图像文件
    sample_image_path = None
    for sample in data['train'][:5]:  # 检查前5个样本
        for img_path in sample['image_path']:
            full_path = os.path.join(image_dir, img_path)
            if os.path.exists(full_path):
                sample_image_path = full_path
                break
        if sample_image_path:
            break
    
    if sample_image_path:
        print(f"使用示例图像: {sample_image_path}")
        visualize_image_preprocessing(sample_image_path, val_transform, "验证时图像预处理")
    else:
        print("未找到可用的示例图像")
        
except FileNotFoundError:
    print("数据集文件未找到，跳过图像预处理演示")

class MedicalReportTokenizer:
    """
    医学报告分词器
    
    功能：
    1. 文本清理
    2. 分词
    3. 词汇表构建
    4. 编码/解码
    """
    
    def __init__(self, threshold=3):
        """
        初始化分词器
        
        Args:
            threshold (int): 词汇频率阈值，低于此值的词汇将被标记为<unk>
        """
        self.threshold = threshold
        self.token2idx = {}  # 词汇到索引的映射
        self.idx2token = {}  # 索引到词汇的映射
        
        # 特殊标记
        self.special_tokens = {
            '<pad>': 0,  # 填充标记
            '<unk>': 1,  # 未知词汇标记
            '<start>': 2,  # 序列开始标记
            '<end>': 3   # 序列结束标记
        }
    
    def clean_report(self, report):
        """
        清理报告文本
        
        Args:
            report (str): 原始报告文本
        
        Returns:
            str: 清理后的文本
        """
        # 转换为小写
        report = report.lower()
        
        # 移除多余的空白字符
        report = re.sub(r'\s+', ' ', report)
        
        # 处理标点符号（保留句号，移除其他标点）
        report = re.sub(r'[.,?;*!%^&_+():-\[\]{}]', '', report)
        report = re.sub(r'["\'/\\]', '', report)
        
        # 分割句子并清理
        sentences = report.split('.')
        cleaned_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence:  # 非空句子
                cleaned_sentences.append(sentence)
        
        # 重新组合，用句号分隔
        cleaned_report = ' . '.join(cleaned_sentences)
        if cleaned_report and not cleaned_report.endswith(' .'):
            cleaned_report += ' .'
        
        return cleaned_report.strip()
    
    def build_vocabulary(self, reports):
        """
        从报告列表构建词汇表
        
        Args:
            reports (list): 报告文本列表
        """
        print("构建词汇表...")
        
        # 收集所有词汇
        all_tokens = []
        for report in reports:
            cleaned_report = self.clean_report(report)
            tokens = cleaned_report.split()
            all_tokens.extend(tokens)
        
        # 统计词频
        token_counts = Counter(all_tokens)
        print(f"总词汇数: {len(all_tokens)}")
        print(f"唯一词汇数: {len(token_counts)}")
        
        # 过滤低频词汇
        vocab = [token for token, count in token_counts.items() 
                if count >= self.threshold]
        vocab.sort()  # 排序以确保一致性
        
        print(f"过滤后词汇数: {len(vocab)} (阈值: {self.threshold})")
        
        # 构建映射
        self.token2idx = self.special_tokens.copy()
        self.idx2token = {idx: token for token, idx in self.special_tokens.items()}
        
        # 添加词汇表中的词汇
        for token in vocab:
            if token not in self.token2idx:
                idx = len(self.token2idx)
                self.token2idx[token] = idx
                self.idx2token[idx] = token
        
        print(f"最终词汇表大小: {len(self.token2idx)}")
    
    def encode(self, report):
        """
        将报告文本编码为数字序列
        
        Args:
            report (str): 报告文本
        
        Returns:
            list: 数字序列
        """
        cleaned_report = self.clean_report(report)
        tokens = cleaned_report.split()
        
        # 添加开始和结束标记
        sequence = [self.token2idx['<start>']]
        
        for token in tokens:
            if token in self.token2idx:
                sequence.append(self.token2idx[token])
            else:
                sequence.append(self.token2idx['<unk>'])
        
        sequence.append(self.token2idx['<end>'])
        return sequence
    
    def decode(self, sequence):
        """
        将数字序列解码为文本
        
        Args:
            sequence (list): 数字序列
        
        Returns:
            str: 解码后的文本
        """
        tokens = []
        for idx in sequence:
            if idx in self.idx2token:
                token = self.idx2token[idx]
                if token not in ['<start>', '<end>', '<pad>']:
                    tokens.append(token)
            else:
                break  # 遇到无效索引时停止
        
        return ' '.join(tokens)
    
    def get_vocab_size(self):
        """获取词汇表大小"""
        return len(self.token2idx)

# 演示分词器的使用
print("创建医学报告分词器...")
tokenizer = MedicalReportTokenizer(threshold=3)

# 使用数据集中的报告来构建词汇表和演示
try:
    # 加载数据
    with open(annotation_path, 'r') as f:
        data = json.load(f)
    
    # 收集训练集的报告用于构建词汇表
    train_reports = [sample['report'] for sample in data['train']]
    
    # 构建词汇表
    tokenizer.build_vocabulary(train_reports)
    
    # 演示文本处理
    sample_report = train_reports[0]
    print(f"\n原始报告:")
    print(f"\"{sample_report}\"")
    
    # 清理文本
    cleaned = tokenizer.clean_report(sample_report)
    print(f"\n清理后:")
    print(f"\"{cleaned}\"")
    
    # 编码
    encoded = tokenizer.encode(sample_report)
    print(f"\n编码后 (前20个数字):")
    print(encoded[:20])
    
    # 解码
    decoded = tokenizer.decode(encoded)
    print(f"\n解码后:")
    print(f"\"{decoded}\"")
    
    # 显示词汇表统计
    print(f"\n词汇表统计:")
    print(f"词汇表大小: {tokenizer.get_vocab_size()}")
    print(f"特殊标记: {list(tokenizer.special_tokens.keys())}")
    
    # 显示一些高频词汇
    print(f"\n前20个词汇 (按索引):")
    for i in range(min(20, len(tokenizer.idx2token))):
        print(f"{i:2d}: {tokenizer.idx2token[i]}")
        
except FileNotFoundError:
    print("数据集文件未找到，使用示例文本演示分词器")
    
    # 使用示例报告
    example_reports = [
        "The heart size is normal. The lungs are clear. No pneumothorax or pleural effusion.",
        "Cardiac silhouette is within normal limits. No focal consolidation. No pleural effusion.",
        "Normal heart size. Clear lungs. No acute findings."
    ]
    
    tokenizer.build_vocabulary(example_reports)
    
    for i, report in enumerate(example_reports):
        print(f"\n示例 {i+1}:")
        print(f"原文: {report}")
        encoded = tokenizer.encode(report)
        decoded = tokenizer.decode(encoded)
        print(f"编码: {encoded}")
        print(f"解码: {decoded}")

class IUXrayDataset(Dataset):
    """
    IU X-Ray数据集类
    
    功能：
    1. 加载图像和报告
    2. 应用预处理变换
    3. 返回批处理友好的数据格式
    """
    
    def __init__(self, data_samples, image_dir, tokenizer, transform=None, max_seq_length=60):
        """
        初始化数据集
        
        Args:
            data_samples (list): 数据样本列表
            image_dir (str): 图像目录路径
            tokenizer: 分词器实例
            transform: 图像变换
            max_seq_length (int): 最大序列长度
        """
        self.data_samples = data_samples
        self.image_dir = image_dir
        self.tokenizer = tokenizer
        self.transform = transform
        self.max_seq_length = max_seq_length
    
    def __len__(self):
        """返回数据集大小"""
        return len(self.data_samples)
    
    def __getitem__(self, idx):
        """
        获取单个数据样本
        
        Args:
            idx (int): 样本索引
        
        Returns:
            tuple: (image_id, images, report_ids, report_mask, seq_length)
        """
        sample = self.data_samples[idx]
        
        # 获取样本信息
        image_id = sample['id']
        image_paths = sample['image_path']
        report = sample['report']
        
        # 加载和预处理图像
        images = []
        for img_path in image_paths:
            full_path = os.path.join(self.image_dir, img_path)
            try:
                image = Image.open(full_path).convert('RGB')
                if self.transform:
                    image = self.transform(image)
                images.append(image)
            except Exception as e:
                print(f"加载图像失败 {full_path}: {e}")
                # 创建一个零图像作为占位符
                if self.transform:
                    dummy_image = torch.zeros(3, 224, 224)
                else:
                    dummy_image = torch.zeros(3, 256, 256)
                images.append(dummy_image)
        
        # 如果只有一张图像，复制一份（IU X-Ray通常有两张图像）
        while len(images) < 2:
            images.append(images[0].clone() if images else torch.zeros(3, 224, 224))
        
        # 堆叠图像
        images = torch.stack(images[:2])  # 只取前两张图像
        
        # 处理报告文本
        report_ids = self.tokenizer.encode(report)
        
        # 截断或填充到最大长度
        if len(report_ids) > self.max_seq_length:
            report_ids = report_ids[:self.max_seq_length]
        
        # 创建掩码（1表示真实词汇，0表示填充）
        seq_length = len(report_ids)
        report_mask = [1] * seq_length
        
        # 填充到最大长度
        pad_length = self.max_seq_length - seq_length
        if pad_length > 0:
            report_ids.extend([self.tokenizer.token2idx['<pad>']] * pad_length)
            report_mask.extend([0] * pad_length)
        
        return (
            image_id,
            images,
            torch.tensor(report_ids, dtype=torch.long),
            torch.tensor(report_mask, dtype=torch.long),
            seq_length
        )

print("IU X-Ray数据集类定义完成")

def collate_fn(batch):
    """
    自定义批处理函数
    
    Args:
        batch (list): 批次数据列表
    
    Returns:
        tuple: 批处理后的数据
    """
    # 分离各个组件
    image_ids, images, report_ids, report_masks, seq_lengths = zip(*batch)
    
    # 堆叠张量
    images = torch.stack(images, 0)  # (batch_size, 2, 3, 224, 224)
    report_ids = torch.stack(report_ids, 0)  # (batch_size, max_seq_length)
    report_masks = torch.stack(report_masks, 0)  # (batch_size, max_seq_length)
    
    return image_ids, images, report_ids, report_masks

def create_data_loaders(data, image_dir, tokenizer, batch_size=4, num_workers=2):
    """
    创建训练、验证和测试数据加载器
    
    Args:
        data (dict): 包含train/val/test分割的数据
        image_dir (str): 图像目录
        tokenizer: 分词器
        batch_size (int): 批大小
        num_workers (int): 数据加载进程数
    
    Returns:
        tuple: (train_loader, val_loader, test_loader)
    """
    # 创建数据集
    train_dataset = IUXrayDataset(
        data['train'], image_dir, tokenizer, 
        transform=create_image_transforms(is_training=True)
    )
    
    val_dataset = IUXrayDataset(
        data['val'], image_dir, tokenizer,
        transform=create_image_transforms(is_training=False)
    )
    
    test_dataset = IUXrayDataset(
        data['test'], image_dir, tokenizer,
        transform=create_image_transforms(is_training=False)
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, shuffle=True,
        collate_fn=collate_fn, num_workers=num_workers
    )
    
    val_loader = DataLoader(
        val_dataset, batch_size=batch_size, shuffle=False,
        collate_fn=collate_fn, num_workers=num_workers
    )
    
    test_loader = DataLoader(
        test_dataset, batch_size=batch_size, shuffle=False,
        collate_fn=collate_fn, num_workers=num_workers
    )
    
    return train_loader, val_loader, test_loader

print("数据加载器函数定义完成")

try:
    # 创建数据加载器
    train_loader, val_loader, test_loader = create_data_loaders(
        data, image_dir, tokenizer, batch_size=2, num_workers=0  # 使用小批次和0个worker便于调试
    )
    
    print(f"数据加载器创建成功:")
    print(f"训练集批次数: {len(train_loader)}")
    print(f"验证集批次数: {len(val_loader)}")
    print(f"测试集批次数: {len(test_loader)}")
    
    # 获取一个训练批次
    print("\n获取一个训练批次...")
    for batch_idx, (image_ids, images, report_ids, report_masks) in enumerate(train_loader):
        print(f"\n批次 {batch_idx + 1}:")
        print(f"图像ID: {image_ids}")
        print(f"图像张量形状: {images.shape}")
        print(f"报告ID张量形状: {report_ids.shape}")
        print(f"报告掩码张量形状: {report_masks.shape}")
        
        # 显示第一个样本的详细信息
        print(f"\n第一个样本详情:")
        print(f"图像像素值范围: [{images[0].min():.3f}, {images[0].max():.3f}]")
        print(f"报告ID (前20个): {report_ids[0][:20].tolist()}")
        print(f"报告掩码 (前20个): {report_masks[0][:20].tolist()}")
        
        # 解码报告
        decoded_report = tokenizer.decode(report_ids[0].tolist())
        print(f"解码后的报告: {decoded_report}")
        
        break  # 只查看第一个批次
    
    # 可视化批次中的图像
    def visualize_batch(images, image_ids, max_samples=2):
        """
        可视化批次中的图像
        
        Args:
            images (torch.Tensor): 图像张量 (batch_size, 2, 3, 224, 224)
            image_ids (list): 图像ID列表
            max_samples (int): 最大显示样本数
        """
        batch_size = min(images.shape[0], max_samples)
        
        fig, axes = plt.subplots(batch_size, 2, figsize=(10, 5 * batch_size))
        if batch_size == 1:
            axes = axes.reshape(1, -1)
        
        # 反标准化参数
        mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1)
        
        for i in range(batch_size):
            for j in range(2):  # 两张图像
                # 反标准化
                img = images[i, j] * std[0] + mean[0]
                img = torch.clamp(img, 0, 1)
                
                # 转换为numpy并调整维度
                img_np = img.permute(1, 2, 0).numpy()
                
                axes[i, j].imshow(img_np)
                axes[i, j].set_title(f"样本 {image_ids[i]} - 图像 {j+1}")
                axes[i, j].axis('off')
        
        plt.tight_layout()
        plt.show()
    
    print("\n可视化批次图像:")
    visualize_batch(images, image_ids)
    
except Exception as e:
    print(f"创建数据加载器时出错: {e}")
    print("这可能是因为图像文件不存在或路径不正确")

def create_preprocessing_pipeline(annotation_path, image_dir, batch_size=4, max_seq_length=60):
    """
    创建完整的数据预处理管道
    
    Args:
        annotation_path (str): 标注文件路径
        image_dir (str): 图像目录路径
        batch_size (int): 批大小
        max_seq_length (int): 最大序列长度
    
    Returns:
        tuple: (tokenizer, train_loader, val_loader, test_loader)
    """
    print("创建数据预处理管道...")
    
    # 1. 加载数据
    print("1. 加载数据...")
    with open(annotation_path, 'r') as f:
        data = json.load(f)
    
    # 2. 创建分词器并构建词汇表
    print("2. 构建词汇表...")
    tokenizer = MedicalReportTokenizer(threshold=3)
    train_reports = [sample['report'] for sample in data['train']]
    tokenizer.build_vocabulary(train_reports)
    
    # 3. 创建数据加载器
    print("3. 创建数据加载器...")
    train_loader, val_loader, test_loader = create_data_loaders(
        data, image_dir, tokenizer, batch_size=batch_size, num_workers=0
    )
    
    print("数据预处理管道创建完成！")
    
    # 打印统计信息
    print(f"\n统计信息:")
    print(f"词汇表大小: {tokenizer.get_vocab_size()}")
    print(f"最大序列长度: {max_seq_length}")
    print(f"批大小: {batch_size}")
    print(f"训练批次数: {len(train_loader)}")
    print(f"验证批次数: {len(val_loader)}")
    print(f"测试批次数: {len(test_loader)}")
    
    return tokenizer, train_loader, val_loader, test_loader

# 演示完整管道
try:
    tokenizer, train_loader, val_loader, test_loader = create_preprocessing_pipeline(
        annotation_path, image_dir, batch_size=2, max_seq_length=60
    )
    print("\n✅ 数据预处理管道创建成功！")
except Exception as e:
    print(f"❌ 创建预处理管道失败: {e}")